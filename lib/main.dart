
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/calorie_service.dart';
import 'screens/home_screen.dart';
import 'utils/constants.dart';

void main() {
  runApp(const CalorieTrackerApp());
}

class CalorieTrackerApp extends StatelessWidget {
  const CalorieTrackerApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CalorieService(),
      child: MaterialApp(
        title: AppConstants.appTitle,
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          // Primary color scheme
          primarySwatch: Colors.green,
          primaryColor: AppConstants.primaryColor,
          
          // App bar theme
          appBarTheme: const AppBarTheme(
            backgroundColor: AppConstants.primaryColor,
            foregroundColor: Colors.white,
            elevation: 0,
            centerTitle: true,
            titleTextStyle: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          
          // Scaffold theme
          scaffoldBackgroundColor: AppConstants.backgroundColor,
          
          // Card theme
          cardTheme: CardTheme(
            color: AppConstants.cardColor,
            elevation: AppConstants.cardElevation,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
          ),
          
          // Elevated button theme
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
              elevation: AppConstants.cardElevation,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
            ),
          ),
          
          // Outlined button theme
          outlinedButtonTheme: OutlinedButtonThemeData(
            style: OutlinedButton.styleFrom(
              foregroundColor: AppConstants.primaryColor,
              side: const BorderSide(color: AppConstants.primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
            ),
          ),
          
          // Text button theme
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: AppConstants.primaryColor,
            ),
          ),
          
          // Input decoration theme
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              borderSide: const BorderSide(color: AppConstants.primaryColor),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          
          // Floating action button theme
          floatingActionButtonTheme: const FloatingActionButtonThemeData(
            backgroundColor: AppConstants.primaryColor,
            foregroundColor: Colors.white,
          ),
          
          // Snack bar theme
          snackBarTheme: SnackBarThemeData(
            backgroundColor: AppConstants.primaryColor,
            contentTextStyle: const TextStyle(color: Colors.white),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            behavior: SnackBarBehavior.floating,
          ),
          
          // Dialog theme
          dialogTheme: DialogThemeData(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
          ),
          
          // Progress indicator theme
          progressIndicatorTheme: const ProgressIndicatorThemeData(
            color: AppConstants.primaryColor,
          ),
          
          // Text theme
          textTheme: const TextTheme(
            headlineLarge: TextStyle(
              color: AppConstants.textPrimary,
              fontWeight: FontWeight.bold,
            ),
            headlineMedium: TextStyle(
              color: AppConstants.textPrimary,
              fontWeight: FontWeight.bold,
            ),
            headlineSmall: TextStyle(
              color: AppConstants.textPrimary,
              fontWeight: FontWeight.w600,
            ),
            bodyLarge: TextStyle(
              color: AppConstants.textPrimary,
            ),
            bodyMedium: TextStyle(
              color: AppConstants.textPrimary,
            ),
            bodySmall: TextStyle(
              color: AppConstants.textSecondary,
            ),
          ),
          
          // Color scheme
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppConstants.primaryColor,
            brightness: Brightness.light,
          ).copyWith(
            primary: AppConstants.primaryColor,
            secondary: AppConstants.secondaryColor,
            error: AppConstants.errorColor,
            background: AppConstants.backgroundColor,
            surface: AppConstants.cardColor,
          ),
          
          // Use Material 3
          useMaterial3: true,
        ),
        home: const HomeScreen(),
      ),
    );
  }
}
