import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../services/calorie_service.dart';
import '../services/nutrition_api_service.dart';
import '../models/food_item.dart';
import '../widgets/custom_button.dart';
import '../utils/constants.dart';

class AddCalorieScreen extends StatefulWidget {
  const AddCalorieScreen({Key? key}) : super(key: key);

  @override
  State<AddCalorieScreen> createState() => _AddCalorieScreenState();
}

class _AddCalorieScreenState extends State<AddCalorieScreen> {
  final _formKey = GlobalKey<FormState>();
  final _foodNameController = TextEditingController();
  final _caloriesController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _foodNameController.dispose();
    _caloriesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          AppConstants.addCalorieTitle,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header card
              _buildHeaderCard(),
              const SizedBox(height: 24),
              
              // Form card
              _buildFormCard(),
              const SizedBox(height: 24),
              
              // Action buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppConstants.cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppConstants.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(24),
            ),
            child: const Icon(
              Icons.add_circle,
              color: AppConstants.primaryColor,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Add New Entry',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.textPrimary,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Track your food and calories',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppConstants.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormCard() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppConstants.cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Food name field
          const Text(
            'Food Name',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _foodNameController,
            decoration: InputDecoration(
              hintText: 'e.g., Apple, Chicken Breast, Pizza',
              prefixIcon: const Icon(Icons.restaurant, color: AppConstants.primaryColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: const BorderSide(color: AppConstants.primaryColor),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a food name';
              }
              if (value.trim().length > AppConstants.maxFoodNameLength) {
                return 'Food name must be less than ${AppConstants.maxFoodNameLength} characters';
              }
              return null;
            },
            textCapitalization: TextCapitalization.words,
            maxLength: AppConstants.maxFoodNameLength,
          ),
          const SizedBox(height: 16),
          
          // Calories field
          const Text(
            'Calories',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _caloriesController,
            decoration: InputDecoration(
              hintText: 'Enter calorie amount',
              prefixIcon: const Icon(Icons.local_fire_department, color: AppConstants.primaryColor),
              suffixText: 'cal',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: const BorderSide(color: AppConstants.primaryColor),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(4),
            ],
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter calorie amount';
              }
              final calories = int.tryParse(value.trim());
              if (calories == null) {
                return 'Please enter a valid number';
              }
              if (calories <= 0) {
                return 'Calories must be greater than 0';
              }
              if (calories > AppConstants.maxCaloriesPerEntry) {
                return 'Calories must be less than ${AppConstants.maxCaloriesPerEntry}';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        CustomButton(
          text: 'Add Entry',
          onPressed: _isLoading ? () {} : _addEntry,
          isLoading: _isLoading,
          icon: Icons.add,
        ),
        const SizedBox(height: 12),
        CustomButton(
          text: 'Cancel',
          onPressed: _isLoading ? () {} : () => Navigator.of(context).pop(),
          isOutlined: true,
          icon: Icons.cancel,
        ),
      ],
    );
  }

  Future<void> _addEntry() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final foodName = _foodNameController.text.trim();
      final calories = int.parse(_caloriesController.text.trim());

      final calorieService = Provider.of<CalorieService>(context, listen: false);
      calorieService.addEntry(foodName, calories);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Added $foodName ($calories cal) successfully!'),
            backgroundColor: AppConstants.primaryColor,
            duration: const Duration(seconds: 2),
          ),
        );

        // Navigate back to home screen
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to add entry. Please try again.'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
