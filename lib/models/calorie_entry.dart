import 'food_item.dart';

class CalorieEntry {
  final String id;
  final String foodName;
  final int calories;
  final DateTime dateTime;
  final FoodItem? foodItem; // Optional reference to the original food item
  final double? protein;
  final double? carbohydrates;
  final double? fat;

  CalorieEntry({
    required this.id,
    required this.foodName,
    required this.calories,
    required this.dateTime,
    this.foodItem,
    this.protein,
    this.carbohydrates,
    this.fat,
  });

  // Create a copy of the entry with updated values
  CalorieEntry copyWith({
    String? id,
    String? foodName,
    int? calories,
    DateTime? dateTime,
    FoodItem? foodItem,
    double? protein,
    double? carbohydrates,
    double? fat,
  }) {
    return CalorieEntry(
      id: id ?? this.id,
      foodName: foodName ?? this.foodName,
      calories: calories ?? this.calories,
      dateTime: dateTime ?? this.dateTime,
      foodItem: foodItem ?? this.foodItem,
      protein: protein ?? this.protein,
      carbohydrates: carbohydrates ?? this.carbohydrates,
      fat: fat ?? this.fat,
    );
  }

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'foodName': foodName,
      'calories': calories,
      'dateTime': dateTime.toIso8601String(),
      'foodItem': foodItem?.toJson(),
      'protein': protein,
      'carbohydrates': carbohydrates,
      'fat': fat,
    };
  }

  // Create from JSON
  factory CalorieEntry.fromJson(Map<String, dynamic> json) {
    return CalorieEntry(
      id: json['id'],
      foodName: json['foodName'],
      calories: json['calories'],
      dateTime: DateTime.parse(json['dateTime']),
      foodItem: json['foodItem'] != null ? FoodItem.fromJson(json['foodItem']) : null,
      protein: json['protein']?.toDouble(),
      carbohydrates: json['carbohydrates']?.toDouble(),
      fat: json['fat']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'CalorieEntry{id: $id, foodName: $foodName, calories: $calories, dateTime: $dateTime}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CalorieEntry &&
        other.id == id &&
        other.foodName == foodName &&
        other.calories == calories &&
        other.dateTime == dateTime &&
        other.foodItem == foodItem &&
        other.protein == protein &&
        other.carbohydrates == carbohydrates &&
        other.fat == fat;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        foodName.hashCode ^
        calories.hashCode ^
        dateTime.hashCode ^
        foodItem.hashCode ^
        protein.hashCode ^
        carbohydrates.hashCode ^
        fat.hashCode;
  }
}
