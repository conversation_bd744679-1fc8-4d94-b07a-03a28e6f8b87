class FoodItem {
  final int fdcId;
  final String description;
  final int calories;
  final double protein;
  final double carbohydrates;
  final double fat;

  FoodItem({
    required this.fdcId,
    required this.description,
    required this.calories,
    this.protein = 0.0,
    this.carbohydrates = 0.0,
    this.fat = 0.0,
  });

  /// Create a copy of the food item with updated values
  FoodItem copyWith({
    int? fdcId,
    String? description,
    int? calories,
    double? protein,
    double? carbohydrates,
    double? fat,
  }) {
    return FoodItem(
      fdcId: fdcId ?? this.fdcId,
      description: description ?? this.description,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbohydrates: carbohydrates ?? this.carbohydrates,
      fat: fat ?? this.fat,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'fdcId': fdcId,
      'description': description,
      'calories': calories,
      'protein': protein,
      'carbohydrates': carbohydrates,
      'fat': fat,
    };
  }

  /// Create from JSON
  factory FoodItem.fromJson(Map<String, dynamic> json) {
    return FoodItem(
      fdcId: json['fdcId'] ?? 0,
      description: json['description'] ?? '',
      calories: json['calories'] ?? 0,
      protein: (json['protein'] ?? 0.0).toDouble(),
      carbohydrates: (json['carbohydrates'] ?? 0.0).toDouble(),
      fat: (json['fat'] ?? 0.0).toDouble(),
    );
  }

  /// Get a formatted display name for the food item
  String get displayName {
    // Clean up the description for better display
    String cleaned = description
        .replaceAll(RegExp(r'\b(UPC|GTIN):\s*\d+\b'), '') // Remove UPC/GTIN codes
        .replaceAll(RegExp(r'\s+'), ' ') // Replace multiple spaces with single space
        .trim();
    
    // Capitalize first letter of each word
    return cleaned.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  /// Get formatted nutritional summary
  String get nutritionalSummary {
    List<String> parts = [];
    
    if (calories > 0) parts.add('${calories} cal');
    if (protein > 0) parts.add('${protein.toStringAsFixed(1)}g protein');
    if (carbohydrates > 0) parts.add('${carbohydrates.toStringAsFixed(1)}g carbs');
    if (fat > 0) parts.add('${fat.toStringAsFixed(1)}g fat');
    
    return parts.join(' • ');
  }

  @override
  String toString() {
    return 'FoodItem{fdcId: $fdcId, description: $description, calories: $calories, protein: $protein, carbs: $carbohydrates, fat: $fat}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FoodItem &&
        other.fdcId == fdcId &&
        other.description == description &&
        other.calories == calories &&
        other.protein == protein &&
        other.carbohydrates == carbohydrates &&
        other.fat == fat;
  }

  @override
  int get hashCode {
    return fdcId.hashCode ^
        description.hashCode ^
        calories.hashCode ^
        protein.hashCode ^
        carbohydrates.hashCode ^
        fat.hashCode;
  }
}
