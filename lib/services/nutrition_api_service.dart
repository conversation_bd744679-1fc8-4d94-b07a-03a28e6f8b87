import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/food_item.dart';

class NutritionApiService {
  // Using USDA FoodData Central API (free, no API key required for basic search)
  static const String _baseUrl = 'https://api.nal.usda.gov/fdc/v1';
  
  // For production, you should get a free API key from https://fdc.nal.usda.gov/api-key-signup.html
  // For now, we'll use the demo key with limited requests
  static const String _apiKey = 'DEMO_KEY';
  
  /// Search for food items by name
  Future<List<FoodItem>> searchFood(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    try {
      final url = Uri.parse('$_baseUrl/foods/search')
          .replace(queryParameters: {
        'query': query.trim(),
        'api_key': _apiKey,
        'pageSize': '10', // Limit results to 10 items
        'dataType': 'Foundation,SR Legacy', // Focus on reliable data sources
      });

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final foods = data['foods'] as List<dynamic>? ?? [];
        
        return foods.map((foodData) => _parseFoodItem(foodData)).toList();
      } else if (response.statusCode == 429) {
        throw Exception('Too many requests. Please try again later.');
      } else {
        throw Exception('Failed to search food: ${response.statusCode}');
      }
    } catch (e) {
      if (e.toString().contains('Too many requests')) {
        rethrow;
      }
      throw Exception('Network error: Please check your internet connection');
    }
  }

  /// Parse food item from API response
  FoodItem _parseFoodItem(Map<String, dynamic> foodData) {
    final String description = foodData['description'] ?? 'Unknown Food';
    final int fdcId = foodData['fdcId'] ?? 0;
    
    // Extract calories from nutrients
    int calories = 0;
    final nutrients = foodData['foodNutrients'] as List<dynamic>? ?? [];
    
    for (final nutrient in nutrients) {
      final nutrientId = nutrient['nutrientId'];
      final nutrientName = nutrient['nutrientName']?.toString().toLowerCase() ?? '';
      
      // Look for energy/calories (nutrient ID 1008 or names containing 'energy')
      if (nutrientId == 1008 || 
          nutrientName.contains('energy') || 
          nutrientName.contains('calorie')) {
        final value = nutrient['value'];
        if (value != null) {
          calories = (value as num).round();
          break;
        }
      }
    }

    // Extract additional nutritional info
    double protein = 0;
    double carbs = 0;
    double fat = 0;

    for (final nutrient in nutrients) {
      final nutrientId = nutrient['nutrientId'];
      final value = (nutrient['value'] as num?)?.toDouble() ?? 0;
      
      switch (nutrientId) {
        case 1003: // Protein
          protein = value;
          break;
        case 1005: // Carbohydrates
          carbs = value;
          break;
        case 1004: // Total lipid (fat)
          fat = value;
          break;
      }
    }

    return FoodItem(
      fdcId: fdcId,
      description: description,
      calories: calories,
      protein: protein,
      carbohydrates: carbs,
      fat: fat,
    );
  }

  /// Get detailed information for a specific food item
  Future<FoodItem?> getFoodDetails(int fdcId) async {
    try {
      final url = Uri.parse('$_baseUrl/food/$fdcId')
          .replace(queryParameters: {
        'api_key': _apiKey,
      });

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseFoodItem(data);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }
}
