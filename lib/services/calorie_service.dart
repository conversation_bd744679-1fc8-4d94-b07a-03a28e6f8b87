import 'package:flutter/foundation.dart';
import '../models/calorie_entry.dart';
import '../models/food_item.dart';
import '../utils/constants.dart';

class CalorieService extends ChangeNotifier {
  List<CalorieEntry> _entries = [];
  int _dailyGoal = AppConstants.defaultDailyGoal;

  // Getters
  List<CalorieEntry> get entries => List.unmodifiable(_entries);
  int get dailyGoal => _dailyGoal;

  // Get today's entries
  List<CalorieEntry> get todaysEntries {
    final today = DateTime.now();
    return _entries.where((entry) {
      return entry.dateTime.year == today.year &&
          entry.dateTime.month == today.month &&
          entry.dateTime.day == today.day;
    }).toList();
  }

  // Calculate total calories for today
  int get todaysTotalCalories {
    return todaysEntries.fold(0, (sum, entry) => sum + entry.calories);
  }

  // Calculate remaining calories for today
  int get remainingCalories {
    return _dailyGoal - todaysTotalCalories;
  }

  // Check if daily goal is exceeded
  bool get isGoalExceeded => todaysTotalCalories > _dailyGoal;

  // Add a new calorie entry
  void addEntry(String foodName, int calories) {
    final entry = CalorieEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      foodName: foodName,
      calories: calories,
      dateTime: DateTime.now(),
    );

    _entries.add(entry);
    notifyListeners();
  }

  // Add a new calorie entry from a food item
  void addEntryFromFoodItem(FoodItem foodItem) {
    final entry = CalorieEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      foodName: foodItem.displayName,
      calories: foodItem.calories,
      dateTime: DateTime.now(),
      foodItem: foodItem,
      protein: foodItem.protein,
      carbohydrates: foodItem.carbohydrates,
      fat: foodItem.fat,
    );

    _entries.add(entry);
    notifyListeners();
  }

  // Remove a calorie entry
  void removeEntry(String id) {
    _entries.removeWhere((entry) => entry.id == id);
    notifyListeners();
  }

  // Update a calorie entry
  void updateEntry(String id, String foodName, int calories) {
    final index = _entries.indexWhere((entry) => entry.id == id);
    if (index != -1) {
      _entries[index] = _entries[index].copyWith(
        foodName: foodName,
        calories: calories,
      );
      notifyListeners();
    }
  }

  // Update daily goal
  void updateDailyGoal(int newGoal) {
    _dailyGoal = newGoal;
    notifyListeners();
  }

  // Clear all entries (for testing or reset)
  void clearAllEntries() {
    _entries.clear();
    notifyListeners();
  }

  // Get entries for a specific date
  List<CalorieEntry> getEntriesForDate(DateTime date) {
    return _entries.where((entry) {
      return entry.dateTime.year == date.year &&
          entry.dateTime.month == date.month &&
          entry.dateTime.day == date.day;
    }).toList();
  }

  // Get total calories for a specific date
  int getTotalCaloriesForDate(DateTime date) {
    return getEntriesForDate(date).fold(0, (sum, entry) => sum + entry.calories);
  }
}
