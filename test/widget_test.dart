import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:calorie_tracker/main.dart';
import 'package:calorie_tracker/services/calorie_service.dart';
import 'package:calorie_tracker/models/calorie_entry.dart';

void main() {
  group('Calorie Tracker App Tests', () {
    testWidgets('App should start with home screen', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const CalorieTrackerApp());

      // Verify that the home screen is displayed
      expect(find.text('Today\'s Calories'), findsOneWidget);
      expect(find.text('No entries yet today'), findsOneWidget);
      expect(find.byType(FloatingActionButton), findsOneWidget);
    });

    testWidgets('Should navigate to food search screen when FAB is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(const CalorieTrackerApp());

      // Tap the floating action button
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pumpAndSettle();

      // Verify that we navigated to the food search screen
      expect(find.text('Search Food'), findsOneWidget);
      expect(find.text('Search for Food'), findsOneWidget);
      expect(find.text('Find food and get automatic calorie info'), findsOneWidget);
    });

    testWidgets('Should navigate to settings screen', (WidgetTester tester) async {
      await tester.pumpWidget(const CalorieTrackerApp());

      // Tap the settings icon in the app bar
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();

      // Verify that we navigated to the settings screen
      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Daily Calorie Goal'), findsOneWidget);
    });
  });

  group('CalorieService Tests', () {
    late CalorieService calorieService;

    setUp(() {
      calorieService = CalorieService();
    });

    test('Should start with empty entries list', () {
      expect(calorieService.entries, isEmpty);
      expect(calorieService.todaysTotalCalories, equals(0));
    });

    test('Should add calorie entry correctly', () {
      calorieService.addEntry('Apple', 95);
      
      expect(calorieService.entries.length, equals(1));
      expect(calorieService.entries.first.foodName, equals('Apple'));
      expect(calorieService.entries.first.calories, equals(95));
      expect(calorieService.todaysTotalCalories, equals(95));
    });

    test('Should remove calorie entry correctly', () {
      calorieService.addEntry('Apple', 95);
      final entryId = calorieService.entries.first.id;
      
      calorieService.removeEntry(entryId);
      
      expect(calorieService.entries, isEmpty);
      expect(calorieService.todaysTotalCalories, equals(0));
    });

    test('Should update daily goal correctly', () {
      expect(calorieService.dailyGoal, equals(2000)); // Default value
      
      calorieService.updateDailyGoal(2500);
      
      expect(calorieService.dailyGoal, equals(2500));
    });

    test('Should calculate remaining calories correctly', () {
      calorieService.updateDailyGoal(2000);
      calorieService.addEntry('Breakfast', 500);
      
      expect(calorieService.remainingCalories, equals(1500));
    });

    test('Should detect when goal is exceeded', () {
      calorieService.updateDailyGoal(1000);
      calorieService.addEntry('Large Meal', 1200);
      
      expect(calorieService.isGoalExceeded, isTrue);
      expect(calorieService.remainingCalories, equals(-200));
    });
  });

  group('CalorieEntry Model Tests', () {
    test('Should create CalorieEntry correctly', () {
      final dateTime = DateTime.now();
      final entry = CalorieEntry(
        id: '1',
        foodName: 'Apple',
        calories: 95,
        dateTime: dateTime,
      );

      expect(entry.id, equals('1'));
      expect(entry.foodName, equals('Apple'));
      expect(entry.calories, equals(95));
      expect(entry.dateTime, equals(dateTime));
    });

    test('Should convert to and from JSON correctly', () {
      final dateTime = DateTime.now();
      final entry = CalorieEntry(
        id: '1',
        foodName: 'Apple',
        calories: 95,
        dateTime: dateTime,
      );

      final json = entry.toJson();
      final fromJson = CalorieEntry.fromJson(json);

      expect(fromJson.id, equals(entry.id));
      expect(fromJson.foodName, equals(entry.foodName));
      expect(fromJson.calories, equals(entry.calories));
      expect(fromJson.dateTime, equals(entry.dateTime));
    });

    test('Should create copy with updated values', () {
      final entry = CalorieEntry(
        id: '1',
        foodName: 'Apple',
        calories: 95,
        dateTime: DateTime.now(),
      );

      final updatedEntry = entry.copyWith(
        foodName: 'Orange',
        calories: 120,
      );

      expect(updatedEntry.id, equals(entry.id));
      expect(updatedEntry.foodName, equals('Orange'));
      expect(updatedEntry.calories, equals(120));
      expect(updatedEntry.dateTime, equals(entry.dateTime));
    });
  });
}
