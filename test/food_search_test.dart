import 'package:flutter_test/flutter_test.dart';
import 'package:calorie_tracker/services/nutrition_api_service.dart';
import 'package:calorie_tracker/models/food_item.dart';

void main() {
  group('Food Search Tests', () {
    late NutritionApiService nutritionService;

    setUp(() {
      nutritionService = NutritionApiService();
    });

    test('Should create FoodItem correctly', () {
      final foodItem = FoodItem(
        fdcId: 123,
        description: 'Apple, raw',
        calories: 95,
        protein: 0.5,
        carbohydrates: 25.0,
        fat: 0.3,
      );

      expect(foodItem.fdcId, equals(123));
      expect(foodItem.description, equals('Apple, raw'));
      expect(foodItem.calories, equals(95));
      expect(foodItem.protein, equals(0.5));
      expect(foodItem.carbohydrates, equals(25.0));
      expect(foodItem.fat, equals(0.3));
    });

    test('Should format display name correctly', () {
      final foodItem = FoodItem(
        fdcId: 123,
        description: 'APPLE, RAW, WITH SKIN',
        calories: 95,
      );

      expect(foodItem.displayName, equals('Apple, Raw, With Skin'));
    });

    test('Should create nutritional summary', () {
      final foodItem = FoodItem(
        fdcId: 123,
        description: 'Apple',
        calories: 95,
        protein: 0.5,
        carbohydrates: 25.0,
        fat: 0.3,
      );

      final summary = foodItem.nutritionalSummary;
      expect(summary, contains('95 cal'));
      expect(summary, contains('0.5g protein'));
      expect(summary, contains('25.0g carbs'));
      expect(summary, contains('0.3g fat'));
    });

    test('Should handle empty search query', () async {
      final results = await nutritionService.searchFood('');
      expect(results, isEmpty);
    });

    test('Should handle whitespace-only search query', () async {
      final results = await nutritionService.searchFood('   ');
      expect(results, isEmpty);
    });

    // Note: This test requires internet connection and may fail if API is down
    test('Should search for food items', () async {
      try {
        final results = await nutritionService.searchFood('apple');
        // We expect some results for a common food like apple
        expect(results, isNotEmpty);
        
        // Check that results have the expected structure
        for (final foodItem in results) {
          expect(foodItem.fdcId, greaterThan(0));
          expect(foodItem.description, isNotEmpty);
          expect(foodItem.calories, greaterThanOrEqualTo(0));
        }
      } catch (e) {
        // If the API is down or there's a network issue, skip this test
        print('Skipping API test due to: $e');
      }
    }, skip: 'Requires internet connection and working API');
  });

  group('FoodItem JSON Tests', () {
    test('Should convert to and from JSON correctly', () {
      final originalFoodItem = FoodItem(
        fdcId: 123,
        description: 'Test Food',
        calories: 100,
        protein: 5.0,
        carbohydrates: 20.0,
        fat: 2.5,
      );

      final json = originalFoodItem.toJson();
      final recreatedFoodItem = FoodItem.fromJson(json);

      expect(recreatedFoodItem, equals(originalFoodItem));
    });

    test('Should handle missing JSON fields gracefully', () {
      final json = {
        'description': 'Test Food',
        'calories': 100,
      };

      final foodItem = FoodItem.fromJson(json);

      expect(foodItem.fdcId, equals(0));
      expect(foodItem.description, equals('Test Food'));
      expect(foodItem.calories, equals(100));
      expect(foodItem.protein, equals(0.0));
      expect(foodItem.carbohydrates, equals(0.0));
      expect(foodItem.fat, equals(0.0));
    });
  });
}
